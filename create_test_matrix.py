#!/usr/bin/env python3
"""
创建测试用的稀疏矩阵文件（Matrix Market 格式）
"""

import random
import sys

def create_test_matrix(filename, rows, cols, nnz, density_pattern='random'):
    """
    创建测试矩阵
    
    Args:
        filename: 输出文件名
        rows: 行数
        cols: 列数  
        nnz: 非零元素数量
        density_pattern: 密度模式 ('random', 'block', 'mixed')
    """
    
    print(f"创建测试矩阵: {rows}x{cols}, {nnz} 非零元素")
    
    # 生成非零元素
    entries = set()
    
    if density_pattern == 'random':
        # 随机分布
        while len(entries) < nnz:
            row = random.randint(1, rows)
            col = random.randint(1, cols)
            entries.add((row, col))
            
    elif density_pattern == 'block':
        # 块状分布 - 创建一些密集块
        block_size = 50
        num_blocks = max(1, nnz // (block_size * block_size))
        
        for _ in range(num_blocks):
            # 随机选择块的起始位置
            start_row = random.randint(1, rows - block_size)
            start_col = random.randint(1, cols - block_size)
            
            # 在块内随机填充
            block_nnz = min(block_size * block_size, nnz - len(entries))
            block_entries = set()
            
            while len(block_entries) < block_nnz and len(entries) < nnz:
                row = random.randint(start_row, start_row + block_size - 1)
                col = random.randint(start_col, start_col + block_size - 1)
                if (row, col) not in entries:
                    entries.add((row, col))
                    block_entries.add((row, col))
                    
        # 填充剩余的随机元素
        while len(entries) < nnz:
            row = random.randint(1, rows)
            col = random.randint(1, cols)
            entries.add((row, col))
            
    elif density_pattern == 'mixed':
        # 混合模式：一些行很密集，一些行很稀疏
        dense_rows = random.sample(range(1, rows + 1), rows // 10)  # 10% 的行是密集的
        
        dense_nnz = nnz * 7 // 10  # 70% 的非零元素在密集行中
        sparse_nnz = nnz - dense_nnz
        
        # 密集行
        dense_entries = 0
        for row in dense_rows:
            row_nnz = dense_nnz // len(dense_rows)
            if dense_entries + row_nnz > dense_nnz:
                row_nnz = dense_nnz - dense_entries
                
            row_entries = set()
            while len(row_entries) < row_nnz:
                col = random.randint(1, cols)
                if (row, col) not in entries:
                    entries.add((row, col))
                    row_entries.add((row, col))
                    dense_entries += 1
                    
        # 稀疏行
        while len(entries) < nnz:
            row = random.randint(1, rows)
            if row not in dense_rows:
                col = random.randint(1, cols)
                entries.add((row, col))
    
    # 转换为列表并排序
    entries_list = sorted(list(entries))
    
    # 写入文件
    with open(filename, 'w') as f:
        f.write("%%MatrixMarket matrix coordinate real general\n")
        f.write(f"{rows} {cols} {len(entries_list)}\n")
        
        for row, col in entries_list:
            value = random.uniform(-10.0, 10.0)
            f.write(f"{row} {col} {value:.6f}\n")
    
    print(f"矩阵文件已创建: {filename}")
    print(f"实际非零元素数量: {len(entries_list)}")
    print(f"稀疏度: {100.0 * len(entries_list) / (rows * cols):.4f}%")

def main():
    if len(sys.argv) < 2:
        print("用法: python3 create_test_matrix.py <size> [pattern]")
        print("  size: small, medium, large 或自定义 rows,cols,nnz")
        print("  pattern: random, block, mixed (默认: mixed)")
        return
    
    size = sys.argv[1]
    pattern = sys.argv[2] if len(sys.argv) > 2 else 'mixed'
    
    if size == 'small':
        rows, cols, nnz = 1000, 1000, 10000
        filename = 'small_test.mtx'
    elif size == 'medium':
        rows, cols, nnz = 5000, 5000, 100000
        filename = 'medium_test.mtx'
    elif size == 'large':
        rows, cols, nnz = 10000, 10000, 500000
        filename = 'large_test.mtx'
    else:
        try:
            parts = size.split(',')
            rows, cols, nnz = int(parts[0]), int(parts[1]), int(parts[2])
            filename = f'custom_{rows}x{cols}_{nnz}.mtx'
        except:
            print("错误: 无效的大小参数")
            return
    
    create_test_matrix(filename, rows, cols, nnz, pattern)

if __name__ == '__main__':
    main()
