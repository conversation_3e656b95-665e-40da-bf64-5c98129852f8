#!/bin/bash

# 超级优化 SDDMM 编译和运行脚本

echo "=== 超级优化 SDDMM 编译和运行脚本 ==="
echo

# 检查 CUDA 环境
if ! command -v nvcc &> /dev/null; then
    echo "错误: 未找到 nvcc 编译器，请确保 CUDA 已正确安装"
    exit 1
fi

echo "检测到的 CUDA 版本:"
nvcc --version | grep "release"
echo

# 编译参数
NVCC_FLAGS="-O3 -std=c++14 -arch=sm_60 -gencode=arch=compute_60,code=sm_60 -gencode=arch=compute_70,code=sm_70 -gencode=arch=compute_75,code=sm_75 -gencode=arch=compute_80,code=sm_80 -gencode=arch=compute_86,code=sm_86"
NVCC_FLAGS="$NVCC_FLAGS -Xcompiler -fopenmp -lcuda -lcudart -lthrust"
NVCC_FLAGS="$NVCC_FLAGS --expt-relaxed-constexpr --expt-extended-lambda"
NVCC_FLAGS="$NVCC_FLAGS -Xptxas -O3 -use_fast_math"

echo "编译超级优化版本..."
nvcc $NVCC_FLAGS sddmm_super_optimized.cu -o sddmm_super_optimized

if [ $? -ne 0 ]; then
    echo "编译失败！"
    exit 1
fi

echo "编译成功！"
echo

# 创建测试矩阵文件（如果不存在）
if [ ! -f "test_matrix.mtx" ]; then
    echo "创建测试矩阵文件..."
    cat > test_matrix.mtx << EOF
%%MatrixMarket matrix coordinate real general
1000 1000 50000
1 1 1.0
1 2 2.0
1 3 3.0
2 1 4.0
2 2 5.0
3 3 6.0
4 4 7.0
5 5 8.0
EOF
    
    # 生成更多随机非零元素
    python3 -c "
import random
with open('test_matrix.mtx', 'a') as f:
    for i in range(50000-8):
        row = random.randint(1, 1000)
        col = random.randint(1, 1000)
        val = random.uniform(-10, 10)
        f.write(f'{row} {col} {val:.6f}\n')
"
    echo "测试矩阵文件创建完成"
fi

echo "运行超级优化版本..."
echo "=========================="

# 运行不同的 K 值进行测试
for K in 64 128 256; do
    echo
    echo "测试 K=$K:"
    echo "----------"
    ./sddmm_super_optimized test_matrix.mtx $K
    echo
done

echo "=========================="
echo "所有测试完成！"

# 性能对比（如果原版本存在）
if [ -f "sddmm_46" ]; then
    echo
    echo "与原版本性能对比:"
    echo "=================="
    echo "原版本 (K=128):"
    ./sddmm_46 test_matrix.mtx 128
    echo
    echo "超级优化版本 (K=128):"
    ./sddmm_super_optimized test_matrix.mtx 128
fi

echo
echo "脚本执行完成！"
