# 超级优化 SDDMM (Sampled Dense-Dense Matrix Multiplication)

这是一个高度优化的 SDDMM CUDA 实现，专门针对不支持 Tensor Core 的 GPU 进行了深度优化。

## 主要优化特性

### 1. **向量化内存访问**
- 使用 `float4` 向量化加载和计算
- 优化内存合并访问模式
- 减少内存事务数量

### 2. **智能预取机制**
- 预测性预取下一个非零元素的数据
- 使用非临时加载指令优化缓存利用
- 可配置的预取距离

### 3. **多级缓存系统**
- **L0 级别**: 寄存器缓存，每个线程缓存关键数据
- **L1 级别**: 共享内存缓存，避免 bank 冲突
- **L2 级别**: 智能预取到 L2 缓存

### 4. **自适应负载均衡**
- 根据行密度动态选择计算策略
- 工作窃取机制避免负载不均
- 四级密度分类：低/中/高/超高密度

### 5. **高效 Warp 内归约**
- 使用 `__shfl_down_sync` 指令
- 避免共享内存同步开销
- 最小化 warp 分歧

### 6. **内存访问优化**
- 避免共享内存 bank 冲突
- 优化数据布局和对齐
- 使用 `__restrict__` 指针优化

## 文件说明

- `sddmm_super_optimized.cu` - 主要的超级优化实现
- `sddmm_46.cu` - 原始版本（用于性能对比）
- `compile_and_run.bat` - Windows 编译运行脚本
- `compile_and_run.sh` - Linux 编译运行脚本
- `create_test_matrix.py` - 测试矩阵生成工具
- `README.md` - 本文档

## 编译和运行

### Windows 系统
```bash
# 直接运行批处理文件
compile_and_run.bat
```

### Linux 系统
```bash
# 给脚本执行权限
chmod +x compile_and_run.sh

# 运行脚本
./compile_and_run.sh
```

### 手动编译
```bash
nvcc -O3 -std=c++14 -arch=sm_60 \
     -gencode=arch=compute_60,code=sm_60 \
     -gencode=arch=compute_70,code=sm_70 \
     -gencode=arch=compute_75,code=sm_75 \
     -gencode=arch=compute_80,code=sm_80 \
     -gencode=arch=compute_86,code=sm_86 \
     --expt-relaxed-constexpr --expt-extended-lambda \
     -Xptxas -O3 -use_fast_math \
     sddmm_super_optimized.cu -o sddmm_super_optimized
```

## 使用方法

```bash
# 基本用法
./sddmm_super_optimized <matrix_file.mtx> [K]

# 示例
./sddmm_super_optimized test_matrix.mtx 128
```

参数说明：
- `matrix_file.mtx`: Matrix Market 格式的稀疏矩阵文件
- `K`: 密集矩阵的内维度（默认 128）

## 测试矩阵生成

使用提供的 Python 脚本生成不同规模和模式的测试矩阵：

```bash
# 生成小规模测试矩阵
python3 create_test_matrix.py small mixed

# 生成中等规模测试矩阵
python3 create_test_matrix.py medium block

# 生成大规模测试矩阵
python3 create_test_matrix.py large random

# 自定义矩阵 (行数,列数,非零元素数)
python3 create_test_matrix.py 5000,5000,100000 mixed
```

模式说明：
- `random`: 随机分布
- `block`: 块状分布（模拟实际应用中的局部性）
- `mixed`: 混合模式（部分行密集，部分行稀疏）

## 性能特性

### 算法复杂度
- 时间复杂度: O(nnz × K)
- 空间复杂度: O(M × K + N × K + nnz)

### 优化效果
相比原始实现，预期性能提升：
- **内存带宽利用率**: 提升 2-4 倍
- **计算效率**: 提升 3-6 倍
- **整体性能**: 提升 5-10 倍（取决于矩阵特性）

### 适用场景
- 稀疏度 0.1% - 10% 的矩阵
- K 维度 64 - 1024
- 各种密度分布的稀疏矩阵

## 系统要求

- CUDA Compute Capability 6.0 或更高
- CUDA Toolkit 10.0 或更高
- 支持 C++14 的编译器
- 推荐 8GB 或更多 GPU 内存

## 性能调优建议

1. **根据 GPU 架构调整编译参数**
   - 使用对应的 `-arch` 和 `-gencode` 参数

2. **根据矩阵特性调整常量**
   - 修改 `MEDIUM_DENSITY_THRESHOLD` 等阈值
   - 调整 `PREFETCH_DISTANCE` 预取距离

3. **内存优化**
   - 确保输入矩阵按行主序存储
   - 使用内存池减少分配开销

4. **多 GPU 扩展**
   - 可以扩展为多 GPU 并行版本
   - 按行或按块分割矩阵

## 故障排除

### 编译错误
- 确保 CUDA Toolkit 正确安装
- 检查 GPU 架构兼容性
- 验证 C++14 支持

### 运行时错误
- 检查 GPU 内存是否足够
- 验证输入矩阵格式
- 确认 CUDA 驱动版本

### 性能问题
- 使用 `nvprof` 或 `nsight` 进行性能分析
- 检查内存带宽利用率
- 调整块大小和线程配置

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 贡献

欢迎提交 Issue 和 Pull Request！

## 联系方式

如有问题或建议，请通过 GitHub Issues 联系。
