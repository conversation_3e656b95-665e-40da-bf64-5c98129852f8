#include <iostream>
#include <vector>
#include <string>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <chrono>
#include <ctime>
#include <cstdlib>
#include <limits>
#include <iomanip>
#include <cuda_runtime.h>
#include <cassert>
#include <cuda_fp16.h>
#include <cuda_bf16.h>

// Thrust 头文件
#include <thrust/device_ptr.h>
#include <thrust/scan.h>
#include <thrust/execution_policy.h>

// =================================================================
// 混合精度优化常量定义
// =================================================================
const int MEDIUM_DENSITY_THRESHOLD = 32;
const int HIGH_DENSITY_THRESHOLD = 256;
const int CHUNK_SIZE = 256;
const int WARP_SIZE = 32;

// 混合精度参数
const int FP16_VECTOR_SIZE = 8;  // 使用 half8 向量化
const int BF16_VECTOR_SIZE = 4;  // 使用 __nv_bfloat164 向量化
const int ACCUMULATOR_PRECISION_BITS = 32;  // 累加器使用 FP32

// 自适应精度阈值
const float PRECISION_THRESHOLD = 1e-4f;
const int DYNAMIC_PRECISION_CHECK_INTERVAL = 64;

#define CUDA_CHECK(err) { \
    cudaError_t e = err; \
    if (e != cudaSuccess) { \
        printf("Cuda error in file '%s' in line %d : %s.\n", __FILE__, __LINE__, cudaGetErrorString(e)); \
        exit(EXIT_FAILURE); \
    } \
}

struct CSRMatrix {
    int *row_ptr, *col_idx;
    float *values;
    half *values_fp16;
    __nv_bfloat16 *values_bf16;
    int rows, cols, nnz;
};

// =================================================================
// 混合精度核函数 - FP16 版本
// =================================================================

__global__ void __launch_bounds__(512, 2)
sddmm_mixed_precision_fp16_kernel(
    const half *__restrict__ A, const half *__restrict__ B,
    const int *__restrict__ row_ptr, const int *__restrict__ col_idx,
    const int *__restrict__ row_indices, half *__restrict__ result,
    int num_rows, int K) {
    
    const int warp_id = blockIdx.x * (blockDim.x / WARP_SIZE) + threadIdx.x / WARP_SIZE;
    const int lane_id = threadIdx.x % WARP_SIZE;
    
    if (warp_id >= num_rows) return;
    
    const int row = row_indices[warp_id];
    const int row_start = row_ptr[row];
    const int nnz_in_row = row_ptr[row + 1] - row_start;
    
    // 使用共享内存缓存 A 行数据，优化 bank 冲突
    extern __shared__ half shared_mem[];
    const int shared_offset = (threadIdx.x / WARP_SIZE) * (K + 8);  // 避免 bank 冲突
    half *a_row_cache = &shared_mem[shared_offset];
    
    // 向量化加载 A 行数据
    const half2 *a_vec2 = reinterpret_cast<const half2*>(&A[row * K]);
    half2 *a_cache_vec2 = reinterpret_cast<half2*>(a_row_cache);
    
    for (int k2 = lane_id; k2 < (K + 1) / 2; k2 += WARP_SIZE) {
        if (k2 * 2 < K) {
            a_cache_vec2[k2] = a_vec2[k2];
        }
    }
    __syncwarp();
    
    // 处理每个非零元素
    for (int nnz_idx = 0; nnz_idx < nnz_in_row; ++nnz_idx) {
        const int global_idx = row_start + nnz_idx;
        const int col = col_idx[global_idx];
        
        // 使用 FP32 累加器提高精度
        float partial_sum = 0.0f;
        
        // 向量化计算 - 使用 half2
        const half2 *b_vec2 = reinterpret_cast<const half2*>(&B[col * K]);
        
        for (int k2 = lane_id; k2 < (K + 1) / 2; k2 += WARP_SIZE) {
            if (k2 * 2 < K) {
                half2 a_val = a_cache_vec2[k2];
                half2 b_val = b_vec2[k2];
                
                // 使用内置函数进行高效的 half2 乘法
                half2 prod = __hmul2(a_val, b_val);
                partial_sum += __half2float(prod.x) + __half2float(prod.y);
            }
        }
        
        // 高效的 warp 内归约
        #pragma unroll
        for (int offset = WARP_SIZE / 2; offset > 0; offset /= 2) {
            partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
        }
        
        if (lane_id == 0) {
            result[global_idx] = __float2half(partial_sum);
        }
    }
}

// =================================================================
// 混合精度核函数 - BF16 版本
// =================================================================

__global__ void __launch_bounds__(512, 2)
sddmm_mixed_precision_bf16_kernel(
    const __nv_bfloat16 *__restrict__ A, const __nv_bfloat16 *__restrict__ B,
    const int *__restrict__ row_ptr, const int *__restrict__ col_idx,
    const int *__restrict__ row_indices, __nv_bfloat16 *__restrict__ result,
    int num_rows, int K) {
    
    const int warp_id = blockIdx.x * (blockDim.x / WARP_SIZE) + threadIdx.x / WARP_SIZE;
    const int lane_id = threadIdx.x % WARP_SIZE;
    
    if (warp_id >= num_rows) return;
    
    const int row = row_indices[warp_id];
    const int row_start = row_ptr[row];
    const int nnz_in_row = row_ptr[row + 1] - row_start;
    
    // 使用共享内存缓存 A 行数据
    extern __shared__ __nv_bfloat16 shared_mem_bf16[];
    const int shared_offset = (threadIdx.x / WARP_SIZE) * (K + 8);
    __nv_bfloat16 *a_row_cache = &shared_mem_bf16[shared_offset];
    
    // 向量化加载 A 行数据
    const __nv_bfloat162 *a_vec2 = reinterpret_cast<const __nv_bfloat162*>(&A[row * K]);
    __nv_bfloat162 *a_cache_vec2 = reinterpret_cast<__nv_bfloat162*>(a_row_cache);
    
    for (int k2 = lane_id; k2 < (K + 1) / 2; k2 += WARP_SIZE) {
        if (k2 * 2 < K) {
            a_cache_vec2[k2] = a_vec2[k2];
        }
    }
    __syncwarp();
    
    // 处理每个非零元素
    for (int nnz_idx = 0; nnz_idx < nnz_in_row; ++nnz_idx) {
        const int global_idx = row_start + nnz_idx;
        const int col = col_idx[global_idx];
        
        // 使用 FP32 累加器
        float partial_sum = 0.0f;
        
        // 向量化计算 - 使用 __nv_bfloat162
        const __nv_bfloat162 *b_vec2 = reinterpret_cast<const __nv_bfloat162*>(&B[col * K]);
        
        for (int k2 = lane_id; k2 < (K + 1) / 2; k2 += WARP_SIZE) {
            if (k2 * 2 < K) {
                __nv_bfloat162 a_val = a_cache_vec2[k2];
                __nv_bfloat162 b_val = b_vec2[k2];
                
                // BF16 乘法
                __nv_bfloat162 prod = __hmul2(a_val, b_val);
                partial_sum += __bfloat162float(prod.x) + __bfloat162float(prod.y);
            }
        }
        
        // warp 内归约
        #pragma unroll
        for (int offset = WARP_SIZE / 2; offset > 0; offset /= 2) {
            partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
        }
        
        if (lane_id == 0) {
            result[global_idx] = __float2bfloat16(partial_sum);
        }
    }
}

// =================================================================
// 自适应混合精度核函数
// =================================================================

__global__ void __launch_bounds__(512, 2)
sddmm_adaptive_mixed_precision_kernel(
    const half *__restrict__ A_fp16, const half *__restrict__ B_fp16,
    const __nv_bfloat16 *__restrict__ A_bf16, const __nv_bfloat16 *__restrict__ B_bf16,
    const int *__restrict__ row_ptr, const int *__restrict__ col_idx,
    const int *__restrict__ row_indices, float *__restrict__ result,
    const bool *__restrict__ use_fp16_mask, int num_rows, int K) {
    
    const int warp_id = blockIdx.x * (blockDim.x / WARP_SIZE) + threadIdx.x / WARP_SIZE;
    const int lane_id = threadIdx.x % WARP_SIZE;
    
    if (warp_id >= num_rows) return;
    
    const int row = row_indices[warp_id];
    const int row_start = row_ptr[row];
    const int nnz_in_row = row_ptr[row + 1] - row_start;
    const bool use_fp16 = use_fp16_mask[row];
    
    // 动态分配共享内存
    extern __shared__ char shared_mem_dynamic[];
    const int shared_offset = (threadIdx.x / WARP_SIZE) * (K + 8) * (use_fp16 ? sizeof(half) : sizeof(__nv_bfloat16));
    
    if (use_fp16) {
        // 使用 FP16 路径
        half *a_row_cache = reinterpret_cast<half*>(&shared_mem_dynamic[shared_offset]);
        
        // 加载数据
        for (int k = lane_id; k < K; k += WARP_SIZE) {
            a_row_cache[k] = A_fp16[row * K + k];
        }
        __syncwarp();
        
        // 计算
        for (int nnz_idx = 0; nnz_idx < nnz_in_row; ++nnz_idx) {
            const int global_idx = row_start + nnz_idx;
            const int col = col_idx[global_idx];
            
            float partial_sum = 0.0f;
            for (int k = lane_id; k < K; k += WARP_SIZE) {
                partial_sum += __half2float(a_row_cache[k]) * __half2float(B_fp16[col * K + k]);
            }
            
            #pragma unroll
            for (int offset = WARP_SIZE / 2; offset > 0; offset /= 2) {
                partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
            }
            
            if (lane_id == 0) {
                result[global_idx] = partial_sum;
            }
        }
    } else {
        // 使用 BF16 路径
        __nv_bfloat16 *a_row_cache = reinterpret_cast<__nv_bfloat16*>(&shared_mem_dynamic[shared_offset]);
        
        // 加载数据
        for (int k = lane_id; k < K; k += WARP_SIZE) {
            a_row_cache[k] = A_bf16[row * K + k];
        }
        __syncwarp();
        
        // 计算
        for (int nnz_idx = 0; nnz_idx < nnz_in_row; ++nnz_idx) {
            const int global_idx = row_start + nnz_idx;
            const int col = col_idx[global_idx];
            
            float partial_sum = 0.0f;
            for (int k = lane_id; k < K; k += WARP_SIZE) {
                partial_sum += __bfloat162float(a_row_cache[k]) * __bfloat162float(B_bf16[col * K + k]);
            }
            
            #pragma unroll
            for (int offset = WARP_SIZE / 2; offset > 0; offset /= 2) {
                partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
            }
            
            if (lane_id == 0) {
                result[global_idx] = partial_sum;
            }
        }
    }
}

// =================================================================
// 数据类型转换函数
// =================================================================

void convert_fp32_to_fp16(const float* src, half* dst, size_t size) {
    #pragma omp parallel for
    for (size_t i = 0; i < size; ++i) {
        dst[i] = __float2half(src[i]);
    }
}

void convert_fp32_to_bf16(const float* src, __nv_bfloat16* dst, size_t size) {
    #pragma omp parallel for
    for (size_t i = 0; i < size; ++i) {
        dst[i] = __float2bfloat16(src[i]);
    }
}

void convert_fp16_to_fp32(const half* src, float* dst, size_t size) {
    #pragma omp parallel for
    for (size_t i = 0; i < size; ++i) {
        dst[i] = __half2float(src[i]);
    }
}

void convert_bf16_to_fp32(const __nv_bfloat16* src, float* dst, size_t size) {
    #pragma omp parallel for
    for (size_t i = 0; i < size; ++i) {
        dst[i] = __bfloat162float(src[i]);
    }
}
