@echo off
echo === 兼容性优化 SDDMM 编译和运行脚本 ===
echo.

REM 检查 CUDA 环境
where nvcc >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 nvcc 编译器，请确保 CUDA 已正确安装
    pause
    exit /b 1
)

echo 检测到的 CUDA 版本:
nvcc --version | findstr "release"
echo.

REM 基础编译参数（兼容性优先）
set NVCC_FLAGS=-O3 -std=c++11 -arch=sm_35 -use_fast_math -Xptxas -O3

echo 编译兼容性优化版本...
echo 编译参数: %NVCC_FLAGS%

nvcc %NVCC_FLAGS% sddmm_compatible.cu -o sddmm_compatible.exe

if %errorlevel% neq 0 (
    echo 编译失败！尝试使用更基础的编译参数...
    
    REM 回退到最基础的编译参数
    set BASIC_FLAGS=-O2 -std=c++11 -arch=sm_30
    echo 回退编译参数: %BASIC_FLAGS%
    
    nvcc %BASIC_FLAGS% sddmm_compatible.cu -o sddmm_compatible.exe
    
    if %errorlevel% neq 0 (
        echo 编译仍然失败！请检查 CUDA 环境和代码兼容性。
        pause
        exit /b 1
    )
)

echo 编译成功！
echo.

REM 创建测试矩阵文件（如果不存在）
if not exist "test_matrix.mtx" (
    echo 创建测试矩阵文件...
    
    REM 检查是否有 Python
    where python >nul 2>nul
    if %errorlevel% equ 0 (
        python create_test_matrix.py small mixed
        if exist "small_test.mtx" (
            move small_test.mtx test_matrix.mtx
        )
    )
    
    REM 如果 Python 脚本失败，创建一个简单的测试矩阵
    if not exist "test_matrix.mtx" (
        echo 创建简单测试矩阵...
        echo %%MatrixMarket matrix coordinate real general > test_matrix.mtx
        echo 100 100 1000 >> test_matrix.mtx
        echo 1 1 1.0 >> test_matrix.mtx
        echo 1 2 2.0 >> test_matrix.mtx
        echo 2 1 3.0 >> test_matrix.mtx
        echo 2 2 4.0 >> test_matrix.mtx
        echo 3 3 5.0 >> test_matrix.mtx
        echo 4 4 6.0 >> test_matrix.mtx
        echo 5 5 7.0 >> test_matrix.mtx
        echo 6 6 8.0 >> test_matrix.mtx
        echo 7 7 9.0 >> test_matrix.mtx
        echo 8 8 10.0 >> test_matrix.mtx
        
        REM 添加更多随机元素
        for /l %%i in (11,1,1000) do (
            set /a row=%%i*100/1000+1
            set /a col=%%i*100/1000+1
            echo !row! !col! 1.0 >> test_matrix.mtx
        )
    )
    
    echo 测试矩阵文件创建完成
)

echo 运行兼容性优化版本...
echo ==========================
echo.

REM 运行不同的 K 值进行测试
for %%K in (32 64 128) do (
    echo.
    echo 测试 K=%%K:
    echo ----------
    sddmm_compatible.exe test_matrix.mtx %%K
    echo.
)

echo ==========================
echo 所有测试完成！

REM 性能对比（如果原版本存在）
if exist "sddmm_46.exe" (
    echo.
    echo 与原版本性能对比:
    echo ==================
    echo 原版本 ^(K=128^):
    sddmm_46.exe test_matrix.mtx 128 2>nul || echo 原版本运行失败
    echo.
    echo 兼容性优化版本 ^(K=128^):
    sddmm_compatible.exe test_matrix.mtx 128
)

echo.
echo 脚本执行完成！
pause
