#include <iostream>
#include <vector>
#include <string>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <chrono>
#include <ctime>
#include <cstdlib>
#include <limits>
#include <iomanip>
#include <cuda_runtime.h>
#include <cassert>
#include <cooperative_groups.h>

// Thrust 头文件
#include <thrust/device_ptr.h>
#include <thrust/scan.h>
#include <thrust/execution_policy.h>

using namespace cooperative_groups;

// =================================================================
// 超级优化常量定义 (无需 Tensor Core)
// =================================================================
const int MEDIUM_DENSITY_THRESHOLD = 32;
const int HIGH_DENSITY_THRESHOLD = 256;
const int ULTRA_HIGH_DENSITY_THRESHOLD = 1024;
const int CHUNK_SIZE = 256;
const int WARP_SIZE = 32;

// 内存访问优化参数
const int VECTOR_WIDTH = 4;           // float4 向量化
const int PREFETCH_DISTANCE = 8;      // 预取距离
const int CACHE_LINE_SIZE = 128;      // 缓存行大小
const int SHARED_MEM_BANKS = 32;      // 共享内存 bank 数量
const int COALESCING_FACTOR = 4;      // 内存合并因子

// 自适应块大小参数
const int ADAPTIVE_BLOCK_SIZE = 512;
const int MAX_WARPS_PER_BLOCK = 16;
const int MIN_WARPS_PER_BLOCK = 4;

// 负载均衡参数
const int WORK_STEALING_THRESHOLD = 8;
const int DYNAMIC_SCHEDULING_GRANULARITY = 64;

#define CUDA_CHECK(err) { \
    cudaError_t e = err; \
    if (e != cudaSuccess) { \
        printf("CUDA error in file '%s' in line %d : %s.\n", __FILE__, __LINE__, cudaGetErrorString(e)); \
        exit(EXIT_FAILURE); \
    } \
}

struct CSRMatrix {
    int *row_ptr, *col_idx;
    float *values;
    int rows, cols, nnz;
};

// =================================================================
// 超级优化核函数 1: 向量化 + 预取 + 缓存优化
// =================================================================

__global__ void __launch_bounds__(ADAPTIVE_BLOCK_SIZE, 2)
sddmm_vectorized_prefetch_kernel(
    const float *__restrict__ A, const float *__restrict__ B,
    const int *__restrict__ row_ptr, const int *__restrict__ col_idx,
    const int *__restrict__ row_indices, float *__restrict__ result,
    int num_rows, int K) {

    auto block = this_thread_block();
    auto warp = tiled_partition<32>(block);

    const int warp_id = block.thread_rank() / WARP_SIZE;
    const int lane_id = warp.thread_rank();
    const int global_warp_id = blockIdx.x * (blockDim.x / WARP_SIZE) + warp_id;

    if (global_warp_id >= num_rows) return;

    const int row = row_indices[global_warp_id];
    const int row_start = row_ptr[row];
    const int nnz_in_row = row_ptr[row + 1] - row_start;

    // 动态共享内存分配，避免 bank 冲突
    extern __shared__ float shared_mem[];
    const int padded_K = ((K + SHARED_MEM_BANKS - 1) / SHARED_MEM_BANKS) * SHARED_MEM_BANKS;
    float *a_row_cache = &shared_mem[warp_id * padded_K];

    // 向量化加载 A 行数据 - 使用 float4
    const float4 *a_vec4 = reinterpret_cast<const float4*>(&A[row * K]);
    float4 *a_cache_vec4 = reinterpret_cast<float4*>(a_row_cache);

    const int vec4_elements = (K + 3) / 4;
    for (int k4 = lane_id; k4 < vec4_elements; k4 += WARP_SIZE) {
        if (k4 * 4 < K) {
            a_cache_vec4[k4] = a_vec4[k4];
        }
    }
    warp.sync();

    // 处理每个非零元素，使用预取和向量化
    for (int nnz_idx = 0; nnz_idx < nnz_in_row; ++nnz_idx) {
        const int global_idx = row_start + nnz_idx;
        const int col = col_idx[global_idx];

        // 智能预取：预取下几个元素的 B 数据
        if (nnz_idx + PREFETCH_DISTANCE < nnz_in_row) {
            const int prefetch_col = col_idx[row_start + nnz_idx + PREFETCH_DISTANCE];
            // 使用非临时加载指令预取到 L2 缓存
            __builtin_nontemporal_load(&B[prefetch_col * K]);
        }

        float partial_sum = 0.0f;

        // 向量化计算 - 使用 float4
        const float4 *b_vec4 = reinterpret_cast<const float4*>(&B[col * K]);

        for (int k4 = lane_id; k4 < vec4_elements; k4 += WARP_SIZE) {
            if (k4 * 4 < K) {
                float4 a_val = a_cache_vec4[k4];
                float4 b_val = b_vec4[k4];

                // 手动展开向量乘法
                partial_sum += a_val.x * b_val.x;
                partial_sum += a_val.y * b_val.y;
                partial_sum += a_val.z * b_val.z;
                partial_sum += a_val.w * b_val.w;
            }
        }

        // 高效的 warp 内归约 - 使用 shuffle 指令
        partial_sum += warp.shfl_down(partial_sum, 16);
        partial_sum += warp.shfl_down(partial_sum, 8);
        partial_sum += warp.shfl_down(partial_sum, 4);
        partial_sum += warp.shfl_down(partial_sum, 2);
        partial_sum += warp.shfl_down(partial_sum, 1);

        if (lane_id == 0) {
            result[global_idx] = partial_sum;
        }
    }
}

// =================================================================
// 超级优化核函数 2: 自适应负载均衡 + 工作窃取
// =================================================================

__global__ void __launch_bounds__(ADAPTIVE_BLOCK_SIZE, 2)
sddmm_adaptive_load_balance_kernel(
    const float *__restrict__ A, const float *__restrict__ B,
    const int *__restrict__ row_ptr, const int *__restrict__ col_idx,
    const int *__restrict__ row_indices, float *__restrict__ result,
    volatile int *__restrict__ work_queue_head, int num_rows, int K) {

    auto block = this_thread_block();
    auto warp = tiled_partition<32>(block);

    const int warp_id = block.thread_rank() / WARP_SIZE;
    const int lane_id = warp.thread_rank();

    // 动态共享内存
    extern __shared__ float shared_mem[];
    const int padded_K = ((K + SHARED_MEM_BANKS - 1) / SHARED_MEM_BANKS) * SHARED_MEM_BANKS;
    float *a_row_cache = &shared_mem[warp_id * padded_K];

    // 工作窃取循环
    while (true) {
        int work_idx = -1;

        // 原子获取工作项
        if (lane_id == 0) {
            work_idx = atomicAdd((int*)work_queue_head, 1);
        }
        work_idx = warp.shfl(work_idx, 0);

        if (work_idx >= num_rows) break;

        const int row = row_indices[work_idx];
        const int row_start = row_ptr[row];
        const int nnz_in_row = row_ptr[row + 1] - row_start;

        // 向量化加载 A 行数据
        const float4 *a_vec4 = reinterpret_cast<const float4*>(&A[row * K]);
        float4 *a_cache_vec4 = reinterpret_cast<float4*>(a_row_cache);

        const int vec4_elements = (K + 3) / 4;
        for (int k4 = lane_id; k4 < vec4_elements; k4 += WARP_SIZE) {
            if (k4 * 4 < K) {
                a_cache_vec4[k4] = a_vec4[k4];
            }
        }
        warp.sync();

        // 根据密度自适应选择策略
        if (nnz_in_row > ULTRA_HIGH_DENSITY_THRESHOLD) {
            // 超高密度：分块处理
            const int chunk_size = CHUNK_SIZE;
            for (int chunk_start = 0; chunk_start < nnz_in_row; chunk_start += chunk_size) {
                int chunk_end = min(chunk_start + chunk_size, nnz_in_row);

                for (int nnz_idx = chunk_start; nnz_idx < chunk_end; ++nnz_idx) {
                    const int global_idx = row_start + nnz_idx;
                    const int col = col_idx[global_idx];

                    float partial_sum = 0.0f;
                    const float4 *b_vec4 = reinterpret_cast<const float4*>(&B[col * K]);

                    for (int k4 = lane_id; k4 < vec4_elements; k4 += WARP_SIZE) {
                        if (k4 * 4 < K) {
                            float4 a_val = a_cache_vec4[k4];
                            float4 b_val = b_vec4[k4];

                            partial_sum += a_val.x * b_val.x + a_val.y * b_val.y +
                                          a_val.z * b_val.z + a_val.w * b_val.w;
                        }
                    }

                    // 快速 warp 归约
                    partial_sum += warp.shfl_down(partial_sum, 16);
                    partial_sum += warp.shfl_down(partial_sum, 8);
                    partial_sum += warp.shfl_down(partial_sum, 4);
                    partial_sum += warp.shfl_down(partial_sum, 2);
                    partial_sum += warp.shfl_down(partial_sum, 1);

                    if (lane_id == 0) {
                        result[global_idx] = partial_sum;
                    }
                }
            }
        } else if (nnz_in_row > HIGH_DENSITY_THRESHOLD) {
            // 高密度：预取优化
            for (int nnz_idx = 0; nnz_idx < nnz_in_row; ++nnz_idx) {
                const int global_idx = row_start + nnz_idx;
                const int col = col_idx[global_idx];

                // 预取优化
                if (nnz_idx + PREFETCH_DISTANCE < nnz_in_row) {
                    const int prefetch_col = col_idx[row_start + nnz_idx + PREFETCH_DISTANCE];
                    __builtin_nontemporal_load(&B[prefetch_col * K]);
                }

                float partial_sum = 0.0f;
                const float4 *b_vec4 = reinterpret_cast<const float4*>(&B[col * K]);

                for (int k4 = lane_id; k4 < vec4_elements; k4 += WARP_SIZE) {
                    if (k4 * 4 < K) {
                        float4 a_val = a_cache_vec4[k4];
                        float4 b_val = b_vec4[k4];

                        partial_sum += a_val.x * b_val.x + a_val.y * b_val.y +
                                      a_val.z * b_val.z + a_val.w * b_val.w;
                    }
                }

                partial_sum += warp.shfl_down(partial_sum, 16);
                partial_sum += warp.shfl_down(partial_sum, 8);
                partial_sum += warp.shfl_down(partial_sum, 4);
                partial_sum += warp.shfl_down(partial_sum, 2);
                partial_sum += warp.shfl_down(partial_sum, 1);

                if (lane_id == 0) {
                    result[global_idx] = partial_sum;
                }
            }
        } else {
            // 低/中密度：标准优化
            for (int nnz_idx = 0; nnz_idx < nnz_in_row; ++nnz_idx) {
                const int global_idx = row_start + nnz_idx;
                const int col = col_idx[global_idx];

                float partial_sum = 0.0f;
                for (int k = lane_id; k < K; k += WARP_SIZE) {
                    partial_sum += a_row_cache[k] * B[col * K + k];
                }

                partial_sum += warp.shfl_down(partial_sum, 16);
                partial_sum += warp.shfl_down(partial_sum, 8);
                partial_sum += warp.shfl_down(partial_sum, 4);
                partial_sum += warp.shfl_down(partial_sum, 2);
                partial_sum += warp.shfl_down(partial_sum, 1);

                if (lane_id == 0) {
                    result[global_idx] = partial_sum;
                }
            }
        }
    }
}

// =================================================================
// 超级优化核函数 3: 多级缓存 + 数据重用
// =================================================================

__global__ void __launch_bounds__(ADAPTIVE_BLOCK_SIZE, 2)
sddmm_multi_level_cache_kernel(
    const float *__restrict__ A, const float *__restrict__ B,
    const int *__restrict__ row_ptr, const int *__restrict__ col_idx,
    const int *__restrict__ row_indices, float *__restrict__ result,
    int num_rows, int K) {

    auto block = this_thread_block();
    auto warp = tiled_partition<32>(block);

    const int warp_id = block.thread_rank() / WARP_SIZE;
    const int lane_id = warp.thread_rank();
    const int global_warp_id = blockIdx.x * (blockDim.x / WARP_SIZE) + warp_id;

    if (global_warp_id >= num_rows) return;

    const int row = row_indices[global_warp_id];
    const int row_start = row_ptr[row];
    const int nnz_in_row = row_ptr[row + 1] - row_start;

    // 三级缓存系统
    extern __shared__ float shared_mem[];
    const int padded_K = ((K + SHARED_MEM_BANKS - 1) / SHARED_MEM_BANKS) * SHARED_MEM_BANKS;
    float *a_row_cache = &shared_mem[warp_id * padded_K];

    // 寄存器缓存 - L0 级别
    float a_reg_cache[16];  // 每个线程缓存 16 个 A 元素
    const int reg_cache_size = min(16, (K + WARP_SIZE - 1) / WARP_SIZE);

    // 加载 A 行数据到共享内存和寄存器
    const float4 *a_vec4 = reinterpret_cast<const float4*>(&A[row * K]);
    float4 *a_cache_vec4 = reinterpret_cast<float4*>(a_row_cache);

    const int vec4_elements = (K + 3) / 4;
    for (int k4 = lane_id; k4 < vec4_elements; k4 += WARP_SIZE) {
        if (k4 * 4 < K) {
            a_cache_vec4[k4] = a_vec4[k4];
        }
    }

    // 加载到寄存器缓存
    for (int i = 0; i < reg_cache_size; ++i) {
        int k = lane_id * reg_cache_size + i;
        if (k < K) {
            a_reg_cache[i] = A[row * K + k];
        }
    }

    warp.sync();

    // 处理非零元素，利用多级缓存
    for (int nnz_idx = 0; nnz_idx < nnz_in_row; ++nnz_idx) {
        const int global_idx = row_start + nnz_idx;
        const int col = col_idx[global_idx];

        float partial_sum = 0.0f;

        // 首先使用寄存器缓存计算
        for (int i = 0; i < reg_cache_size; ++i) {
            int k = lane_id * reg_cache_size + i;
            if (k < K) {
                partial_sum += a_reg_cache[i] * B[col * K + k];
            }
        }

        // 然后使用共享内存缓存处理剩余部分
        const int remaining_start = WARP_SIZE * reg_cache_size;
        for (int k = remaining_start + lane_id; k < K; k += WARP_SIZE) {
            partial_sum += a_row_cache[k] * B[col * K + k];
        }

        // warp 内归约
        partial_sum += warp.shfl_down(partial_sum, 16);
        partial_sum += warp.shfl_down(partial_sum, 8);
        partial_sum += warp.shfl_down(partial_sum, 4);
        partial_sum += warp.shfl_down(partial_sum, 2);
        partial_sum += warp.shfl_down(partial_sum, 1);

        if (lane_id == 0) {
            result[global_idx] = partial_sum;
        }
    }
}

// =================================================================
// 行分类核函数
// =================================================================

__global__ void classify_rows_super_kernel(
    const int *row_ptr, int rows,
    int *d_low_rows, int *d_medium_rows, int *d_high_rows, int *d_ultra_high_rows,
    int *low_count, int *medium_count, int *high_count, int *ultra_high_count) {

    int row = blockIdx.x * blockDim.x + threadIdx.x;
    if (row >= rows) return;

    int nnz = row_ptr[row + 1] - row_ptr[row];

    if (nnz > 0 && nnz <= MEDIUM_DENSITY_THRESHOLD) {
        int pos = atomicAdd(low_count, 1);
        d_low_rows[pos] = row;
    } else if (nnz > MEDIUM_DENSITY_THRESHOLD && nnz <= HIGH_DENSITY_THRESHOLD) {
        int pos = atomicAdd(medium_count, 1);
        d_medium_rows[pos] = row;
    } else if (nnz > HIGH_DENSITY_THRESHOLD && nnz <= ULTRA_HIGH_DENSITY_THRESHOLD) {
        int pos = atomicAdd(high_count, 1);
        d_high_rows[pos] = row;
    } else if (nnz > ULTRA_HIGH_DENSITY_THRESHOLD) {
        int pos = atomicAdd(ultra_high_count, 1);
        d_ultra_high_rows[pos] = row;
    }
}

// =================================================================
// 超级优化主控函数
// =================================================================

void sddmm_super_optimized_main(
    const float *d_A, const float *d_B, CSRMatrix &sparse,
    const std::vector<int> &h_csr_row_ptr, int K,
    size_t shared_mem_per_block, float &time_total) {

    // 分配行分类数组
    int *d_low_rows, *d_medium_rows, *d_high_rows, *d_ultra_high_rows;
    int *d_low_count, *d_medium_count, *d_high_count, *d_ultra_high_count;

    CUDA_CHECK(cudaMalloc(&d_low_rows, sparse.rows * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_medium_rows, sparse.rows * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_high_rows, sparse.rows * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_ultra_high_rows, sparse.rows * sizeof(int)));

    CUDA_CHECK(cudaMalloc(&d_low_count, sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_medium_count, sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_high_count, sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_ultra_high_count, sizeof(int)));

    CUDA_CHECK(cudaMemset(d_low_count, 0, sizeof(int)));
    CUDA_CHECK(cudaMemset(d_medium_count, 0, sizeof(int)));
    CUDA_CHECK(cudaMemset(d_high_count, 0, sizeof(int)));
    CUDA_CHECK(cudaMemset(d_ultra_high_count, 0, sizeof(int)));

    // 创建多个流进行并行处理
    const int num_streams = 4;
    cudaStream_t streams[num_streams];
    for (int i = 0; i < num_streams; ++i) {
        CUDA_CHECK(cudaStreamCreate(&streams[i]));
    }

    cudaEvent_t start_total, stop_total;
    CUDA_CHECK(cudaEventCreate(&start_total));
    CUDA_CHECK(cudaEventCreate(&stop_total));

    CUDA_CHECK(cudaEventRecord(start_total, streams[0]));

    // 行分类
    dim3 block_classify(256);
    dim3 grid_classify((sparse.rows + block_classify.x - 1) / block_classify.x);
    classify_rows_super_kernel<<<grid_classify, block_classify, 0, streams[0]>>>(
        sparse.row_ptr, sparse.rows,
        d_low_rows, d_medium_rows, d_high_rows, d_ultra_high_rows,
        d_low_count, d_medium_count, d_high_count, d_ultra_high_count);

    // 获取分类结果
    int h_counts[4] = {0, 0, 0, 0};
    CUDA_CHECK(cudaMemcpyAsync(&h_counts[0], d_low_count, sizeof(int), cudaMemcpyDeviceToHost, streams[0]));
    CUDA_CHECK(cudaMemcpyAsync(&h_counts[1], d_medium_count, sizeof(int), cudaMemcpyDeviceToHost, streams[0]));
    CUDA_CHECK(cudaMemcpyAsync(&h_counts[2], d_high_count, sizeof(int), cudaMemcpyDeviceToHost, streams[0]));
    CUDA_CHECK(cudaMemcpyAsync(&h_counts[3], d_ultra_high_count, sizeof(int), cudaMemcpyDeviceToHost, streams[0]));

    CUDA_CHECK(cudaStreamSynchronize(streams[0]));

    printf("超级优化行分类结果: 低(<=%d)=%d, 中(<=%d)=%d, 高(<=%d)=%d, 超高(>%d)=%d\n",
           MEDIUM_DENSITY_THRESHOLD, h_counts[0],
           HIGH_DENSITY_THRESHOLD, h_counts[1],
           ULTRA_HIGH_DENSITY_THRESHOLD, h_counts[2],
           ULTRA_HIGH_DENSITY_THRESHOLD, h_counts[3]);

    // 计算所需的共享内存
    const int padded_K = ((K + SHARED_MEM_BANKS - 1) / SHARED_MEM_BANKS) * SHARED_MEM_BANKS;
    size_t required_shared_mem = MAX_WARPS_PER_BLOCK * padded_K * sizeof(float);

    if (required_shared_mem > shared_mem_per_block) {
        printf("警告: 需要的共享内存 (%zu bytes) 超出限制 (%zu bytes)\n",
               required_shared_mem, shared_mem_per_block);
        // 动态调整参数
        required_shared_mem = shared_mem_per_block * 0.9;  // 留一些余量
    }

    // 处理低密度行：使用向量化预取核函数
    if (h_counts[0] > 0) {
        const int warps_per_block = ADAPTIVE_BLOCK_SIZE / WARP_SIZE;
        dim3 grid_low((h_counts[0] + warps_per_block - 1) / warps_per_block);
        dim3 block_low(ADAPTIVE_BLOCK_SIZE);

        sddmm_vectorized_prefetch_kernel<<<grid_low, block_low, required_shared_mem, streams[0]>>>(
            d_A, d_B, sparse.row_ptr, sparse.col_idx,
            d_low_rows, sparse.values, h_counts[0], K);
    }

    // 处理中密度行：使用多级缓存核函数
    if (h_counts[1] > 0) {
        const int warps_per_block = ADAPTIVE_BLOCK_SIZE / WARP_SIZE;
        dim3 grid_medium((h_counts[1] + warps_per_block - 1) / warps_per_block);
        dim3 block_medium(ADAPTIVE_BLOCK_SIZE);

        sddmm_multi_level_cache_kernel<<<grid_medium, block_medium, required_shared_mem, streams[1]>>>(
            d_A, d_B, sparse.row_ptr, sparse.col_idx,
            d_medium_rows, sparse.values, h_counts[1], K);
    }

    // 处理高密度和超高密度行：使用自适应负载均衡核函数
    if (h_counts[2] > 0 || h_counts[3] > 0) {
        int total_high_rows = h_counts[2] + h_counts[3];

        // 合并高密度和超高密度行
        int *d_combined_high_rows;
        CUDA_CHECK(cudaMalloc(&d_combined_high_rows, total_high_rows * sizeof(int)));

        if (h_counts[2] > 0) {
            CUDA_CHECK(cudaMemcpyAsync(d_combined_high_rows, d_high_rows,
                                     h_counts[2] * sizeof(int), cudaMemcpyDeviceToDevice, streams[2]));
        }
        if (h_counts[3] > 0) {
            CUDA_CHECK(cudaMemcpyAsync(d_combined_high_rows + h_counts[2], d_ultra_high_rows,
                                     h_counts[3] * sizeof(int), cudaMemcpyDeviceToDevice, streams[2]));
        }

        // 工作队列用于动态负载均衡
        volatile int *d_queue_head;
        CUDA_CHECK(cudaMalloc((void**)&d_queue_head, sizeof(int)));
        CUDA_CHECK(cudaMemset((void*)d_queue_head, 0, sizeof(int)));

        // 启动自适应负载均衡核函数
        const int num_blocks = min(256, (total_high_rows + MAX_WARPS_PER_BLOCK - 1) / MAX_WARPS_PER_BLOCK);
        dim3 grid_adaptive(num_blocks);
        dim3 block_adaptive(ADAPTIVE_BLOCK_SIZE);

        sddmm_adaptive_load_balance_kernel<<<grid_adaptive, block_adaptive, required_shared_mem, streams[2]>>>(
            d_A, d_B, sparse.row_ptr, sparse.col_idx,
            d_combined_high_rows, sparse.values,
            d_queue_head, total_high_rows, K);

        CUDA_CHECK(cudaFree(d_combined_high_rows));
        CUDA_CHECK(cudaFree((void*)d_queue_head));
    }

    // 同步所有流
    for (int i = 0; i < num_streams; ++i) {
        CUDA_CHECK(cudaStreamSynchronize(streams[i]));
    }

    CUDA_CHECK(cudaEventRecord(stop_total, streams[0]));
    CUDA_CHECK(cudaEventSynchronize(stop_total));
    CUDA_CHECK(cudaEventElapsedTime(&time_total, start_total, stop_total));

    // 清理资源
    CUDA_CHECK(cudaEventDestroy(start_total));
    CUDA_CHECK(cudaEventDestroy(stop_total));

    for (int i = 0; i < num_streams; ++i) {
        CUDA_CHECK(cudaStreamDestroy(streams[i]));
    }

    CUDA_CHECK(cudaFree(d_low_rows));
    CUDA_CHECK(cudaFree(d_medium_rows));
    CUDA_CHECK(cudaFree(d_high_rows));
    CUDA_CHECK(cudaFree(d_ultra_high_rows));
    CUDA_CHECK(cudaFree(d_low_count));
    CUDA_CHECK(cudaFree(d_medium_count));
    CUDA_CHECK(cudaFree(d_high_count));
    CUDA_CHECK(cudaFree(d_ultra_high_count));
}

// =================================================================
// 辅助函数
// =================================================================

void sddmm_cpu_reference(const float *A, const float *B, const int *row_ptr, const int *col_idx, float *values,
                         int M, int N, int K) {
#pragma omp parallel for schedule(dynamic)
    for (int row = 0; row < M; ++row) {
        int start = row_ptr[row];
        int end = row_ptr[row + 1];
        for (int idx = start; idx < end; ++idx) {
            int col = col_idx[idx];
            float sum = 0.0f;
#pragma GCC ivdep
            for (int k = 0; k < K; ++k) {
                sum += A[(size_t) row * K + k] * B[(size_t) col * K + k];
            }
            values[idx] = sum;
        }
    }
}

void load_coo_matrix(const std::string &filename, int &M, int &N, int &nnz,
                     std::vector<int> &coo_rows, std::vector<int> &coo_cols) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        std::cerr << "错误: 无法打开矩阵文件: " << filename << std::endl;
        exit(1);
    }
    while (file.peek() == '%') file.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
    file >> M >> N >> nnz;
    coo_rows.resize(nnz);
    coo_cols.resize(nnz);
    for (int i = 0; i < nnz; ++i) {
        int r, c;
        file >> r >> c;
        coo_rows[i] = r - 1;
        coo_cols[i] = c - 1;
        file.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
    }
    file.close();
}

void coo_to_csr(int M, int nnz, const std::vector<int> &coo_rows_in, const std::vector<int> &coo_cols_in,
                std::vector<int> &csr_row_ptr, std::vector<int> &csr_col_idx) {
    csr_row_ptr.assign(M + 1, 0);
    std::vector<std::pair<int, int> > coo(nnz);
    for (int i = 0; i < nnz; ++i) coo[i] = {coo_rows_in[i], coo_cols_in[i]};
    std::sort(coo.begin(), coo.end());
    csr_col_idx.resize(nnz);
    for (int i = 0; i < nnz; ++i) {
        csr_col_idx[i] = coo[i].second;
        csr_row_ptr[coo[i].first + 1]++;
    }
    for (int i = 0; i < M; ++i) csr_row_ptr[i + 1] += csr_row_ptr[i];
}

__global__ void warmup_kernel() {
}

// =================================================================
// 主函数
// =================================================================

int main(int argc, char **argv) {
    if (argc < 2) {
        std::cerr << "用法: " << argv[0] << " <matrix_file.mtx> [K=128]" << std::endl;
        return 1;
    }

    std::srand(std::time(nullptr));
    std::string filename = argv[1];
    int K = (argc > 2) ? std::atoi(argv[2]) : 128;

    int device;
    cudaGetDevice(&device);
    cudaDeviceProp prop;
    cudaGetDeviceProperties(&prop, device);
    size_t shared_mem_per_block = prop.sharedMemPerBlock;

    printf("=== 超级优化 GPU 信息 ===\n");
    printf("设备名称: %s (Compute Capability %d.%d)\n", prop.name, prop.major, prop.minor);
    printf("设备共享内存/块: %zu bytes\n", shared_mem_per_block);
    printf("多处理器数量: %d\n", prop.multiProcessorCount);
    printf("每个多处理器的最大线程数: %d\n", prop.maxThreadsPerMultiProcessor);
    printf("全局内存带宽: %.1f GB/s\n",
           2.0 * prop.memoryClockRate * (prop.memoryBusWidth / 8) / 1.0e6);
    printf("\n");

    int M, N, nnz;
    std::vector<int> coo_rows, coo_cols;
    load_coo_matrix(filename, M, N, nnz, coo_rows, coo_cols);

    std::vector<int> h_csr_row_ptr, h_csr_col_idx;
    coo_to_csr(M, nnz, coo_rows, coo_cols, h_csr_row_ptr, h_csr_col_idx);

    std::vector<float> h_A((size_t) M * K), h_B((size_t) N * K);
    for (size_t i = 0; i < (size_t) M * K; ++i) h_A[i] = (rand() % 100 + 1) / 100.0f;
    for (size_t i = 0; i < (size_t) N * K; ++i) h_B[i] = (rand() % 100 + 1) / 100.0f;

    float *d_A, *d_B;
    CSRMatrix sparse;
    sparse.rows = M;
    sparse.cols = N;
    sparse.nnz = nnz;

    CUDA_CHECK(cudaMalloc(&d_A, (size_t)M * K * sizeof(float)));
    CUDA_CHECK(cudaMalloc(&d_B, (size_t)N * K * sizeof(float)));
    CUDA_CHECK(cudaMalloc(&sparse.row_ptr, (M + 1) * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&sparse.col_idx, (size_t)nnz * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&sparse.values, (size_t)nnz * sizeof(float)));

    CUDA_CHECK(cudaMemcpy(d_A, h_A.data(), (size_t)M * K * sizeof(float), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_B, h_B.data(), (size_t)N * K * sizeof(float), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(sparse.row_ptr, h_csr_row_ptr.data(), (M + 1) * sizeof(int), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(sparse.col_idx, h_csr_col_idx.data(), (size_t)nnz * sizeof(int), cudaMemcpyHostToDevice));

    printf("=== 矩阵信息 ===\n");
    printf("矩阵文件: %s\n", filename.c_str());
    printf("矩阵维度: M=%d, N=%d, K=%d\n", M, N, K);
    printf("非零元素: %d (稀疏度: %.4f%%)\n", nnz, 100.0 * nnz / ((double) M * N));
    printf("内存使用: A=%.1fMB, B=%.1fMB, 稀疏矩阵=%.1fMB\n",
           (double)M * K * sizeof(float) / 1e6,
           (double)N * K * sizeof(float) / 1e6,
           (double)nnz * sizeof(float) / 1e6);
    printf("\n");

    std::cout << "预热GPU..." << std::endl;
    warmup_kernel<<<1, 1>>>();
    CUDA_CHECK(cudaDeviceSynchronize());

    printf("=== 开始执行超级优化 SDDMM ===\n");
    printf("优化特性: 向量化 + 预取 + 多级缓存 + 自适应负载均衡 + 工作窃取\n\n");

    const int num_runs = 5;
    std::vector<float> total_times;

    for (int run = 0; run < num_runs; run++) {
        CUDA_CHECK(cudaMemset(sparse.values, 0, (size_t)nnz * sizeof(float)));
        CUDA_CHECK(cudaDeviceSynchronize());

        float ms_total = 0;
        if (K > 0) {
            sddmm_super_optimized_main(d_A, d_B, sparse, h_csr_row_ptr, K,
                                     shared_mem_per_block, ms_total);
        }

        total_times.push_back(ms_total);
        printf("运行 %d: %.3f ms\n", run + 1, ms_total);
    }

    float min_time = K > 0 && !total_times.empty() ? *std::min_element(total_times.begin(), total_times.end()) : 0.0f;
    float avg_time = K > 0 && !total_times.empty()
                         ? std::accumulate(total_times.begin(), total_times.end(), 0.0f) / num_runs
                         : 0.0f;
    double gflops = (K > 0 && min_time > 0) ? (2.0 * (double) nnz * K) / (min_time * 1e6) : 0.0;

    printf("\n=== 超级优化性能统计 ===\n");
    printf("平均总时间: %.3f ms\n", avg_time);
    printf("最佳总时间: %.3f ms\n", min_time);
    printf("峰值性能: %.2f GFLOPS\n", gflops);

    // 理论性能对比
    double theoretical_fp32_gflops = prop.multiProcessorCount *
                                   (prop.major >= 6 ? 128 : 64) *
                                   prop.clockRate * 1e-6;
    printf("理论峰值 (FP32): %.0f GFLOPS\n", theoretical_fp32_gflops);
    printf("计算效率: %.2f%%\n", (gflops / theoretical_fp32_gflops) * 100.0);

    // 内存带宽利用率
    double memory_bandwidth_used = ((double)M * K + (double)N * K + (double)nnz) * sizeof(float) / (min_time * 1e-3) / 1e9;
    double theoretical_bandwidth = 2.0 * prop.memoryClockRate * (prop.memoryBusWidth / 8) / 1.0e6;
    printf("内存带宽利用率: %.1f GB/s / %.1f GB/s = %.2f%%\n",
           memory_bandwidth_used, theoretical_bandwidth,
           (memory_bandwidth_used / theoretical_bandwidth) * 100.0);

    if (K > 0) {
        std::cout << "\n验证计算正确性..." << std::endl;

        std::vector<float> h_values_gpu(nnz);
        CUDA_CHECK(cudaMemcpy(h_values_gpu.data(), sparse.values, (size_t)nnz * sizeof(float), cudaMemcpyDeviceToHost));

        std::vector<float> h_values_cpu(nnz, 0.0f);
        auto cpu_start = std::chrono::high_resolution_clock::now();
        sddmm_cpu_reference(h_A.data(), h_B.data(), h_csr_row_ptr.data(), h_csr_col_idx.data(),
                           h_values_cpu.data(), M, N, K);
        auto cpu_end = std::chrono::high_resolution_clock::now();
        auto cpu_duration = std::chrono::duration_cast<std::chrono::milliseconds>(cpu_end - cpu_start);

        std::cout << "CPU (OMP) 参考实现时间: " << cpu_duration.count() << " ms" << std::endl;
        if (min_time > 0)
            std::cout << "GPU加速比 (vs CPU OMP): " << std::fixed << std::setprecision(2)
                     << (float) cpu_duration.count() / min_time << "x" << std::endl;

        int correct_count = 0;
        float max_error = 0.0f;
        double total_abs_error = 0.0, l1_norm_cpu = 0.0;
        for (int i = 0; i < nnz; i++) {
            float abs_diff = std::abs(h_values_cpu[i] - h_values_gpu[i]);
            total_abs_error += abs_diff;
            l1_norm_cpu += std::abs(h_values_cpu[i]);
            if (abs_diff > max_error) max_error = abs_diff;
            bool is_correct = false;
            if (std::abs(h_values_cpu[i]) > 1e-9) {
                if ((abs_diff / std::abs(h_values_cpu[i])) < 1e-4) is_correct = true;
            } else {
                if (abs_diff < 1e-6) is_correct = true;
            }
            if (is_correct) correct_count++;
        }

        std::cout << "\n=== 验证结果 ===" << std::endl;
        std::cout << std::scientific << "最大绝对误差: " << max_error << std::endl;
        if (l1_norm_cpu > 0) {
            std::cout << "相对L1误差: " << (total_abs_error / l1_norm_cpu) << std::endl;
        }
        std::cout << std::fixed << std::setprecision(4) << "近似正确率: " << (100.0f * correct_count / nnz) << "%" << std::endl;
    }

    CUDA_CHECK(cudaFree(d_A));
    CUDA_CHECK(cudaFree(d_B));
    CUDA_CHECK(cudaFree(sparse.row_ptr));
    CUDA_CHECK(cudaFree(sparse.col_idx));
    CUDA_CHECK(cudaFree(sparse.values));

    std::cout << "\n超级优化程序正常结束。" << std::endl;
    return 0;
}
