@echo off
echo === 超级优化 SDDMM 编译和运行脚本 ===
echo.

REM 检查 CUDA 环境
where nvcc >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 nvcc 编译器，请确保 CUDA 已正确安装
    pause
    exit /b 1
)

echo 检测到的 CUDA 版本:
nvcc --version | findstr "release"
echo.

REM 编译参数
set NVCC_FLAGS=-O3 -std=c++14 -arch=sm_60 -gencode=arch=compute_60,code=sm_60 -gencode=arch=compute_70,code=sm_70 -gencode=arch=compute_75,code=sm_75 -gencode=arch=compute_80,code=sm_80 -gencode=arch=compute_86,code=sm_86
set NVCC_FLAGS=%NVCC_FLAGS% --expt-relaxed-constexpr --expt-extended-lambda
set NVCC_FLAGS=%NVCC_FLAGS% -Xptxas -O3 -use_fast_math

echo 编译超级优化版本...
nvcc %NVCC_FLAGS% sddmm_super_optimized.cu -o sddmm_super_optimized.exe

if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 编译成功！
echo.

REM 创建测试矩阵文件（如果不存在）
if not exist "test_matrix.mtx" (
    echo 创建测试矩阵文件...
    python create_test_matrix.py small mixed
    if exist "small_test.mtx" (
        move small_test.mtx test_matrix.mtx
    ) else (
        echo 创建简单测试矩阵...
        echo %%MatrixMarket matrix coordinate real general > test_matrix.mtx
        echo 100 100 1000 >> test_matrix.mtx
        for /l %%i in (1,1,1000) do (
            set /a row=%%i*100/1000+1
            set /a col=%%i*100/1000+1
            echo !row! !col! 1.0 >> test_matrix.mtx
        )
    )
    echo 测试矩阵文件创建完成
)

echo 运行超级优化版本...
echo ==========================
echo.

REM 运行不同的 K 值进行测试
for %%K in (64 128 256) do (
    echo.
    echo 测试 K=%%K:
    echo ----------
    sddmm_super_optimized.exe test_matrix.mtx %%K
    echo.
)

echo ==========================
echo 所有测试完成！

REM 性能对比（如果原版本存在）
if exist "sddmm_46.exe" (
    echo.
    echo 与原版本性能对比:
    echo ==================
    echo 原版本 ^(K=128^):
    sddmm_46.exe test_matrix.mtx 128
    echo.
    echo 超级优化版本 ^(K=128^):
    sddmm_super_optimized.exe test_matrix.mtx 128
)

echo.
echo 脚本执行完成！
pause
