#!/bin/bash

# 兼容性优化 SDDMM 编译和运行脚本

echo "=== 兼容性优化 SDDMM 编译和运行脚本 ==="
echo

# 检查 CUDA 环境
if ! command -v nvcc &> /dev/null; then
    echo "错误: 未找到 nvcc 编译器，请确保 CUDA 已正确安装"
    exit 1
fi

echo "检测到的 CUDA 版本:"
nvcc --version | grep "release"
echo

# 基础编译参数（兼容性优先）
NVCC_FLAGS="-O3 -std=c++11"

# 检测 GPU 架构
GPU_ARCH=$(nvidia-smi --query-gpu=compute_cap --format=csv,noheader,nounits | head -1 | tr -d '.')
if [ ! -z "$GPU_ARCH" ]; then
    echo "检测到 GPU 计算能力: $GPU_ARCH"
    if [ "$GPU_ARCH" -ge "60" ]; then
        NVCC_FLAGS="$NVCC_FLAGS -arch=sm_60"
    elif [ "$GPU_ARCH" -ge "50" ]; then
        NVCC_FLAGS="$NVCC_FLAGS -arch=sm_50"
    elif [ "$GPU_ARCH" -ge "35" ]; then
        NVCC_FLAGS="$NVCC_FLAGS -arch=sm_35"
    else
        NVCC_FLAGS="$NVCC_FLAGS -arch=sm_30"
    fi
else
    echo "无法检测 GPU 架构，使用默认设置"
    NVCC_FLAGS="$NVCC_FLAGS -arch=sm_35"
fi

# 添加优化参数
NVCC_FLAGS="$NVCC_FLAGS -use_fast_math -Xptxas -O3"

echo "编译兼容性优化版本..."
echo "编译参数: $NVCC_FLAGS"

nvcc $NVCC_FLAGS sddmm_compatible.cu -o sddmm_compatible

if [ $? -ne 0 ]; then
    echo "编译失败！尝试使用更基础的编译参数..."
    
    # 回退到最基础的编译参数
    BASIC_FLAGS="-O2 -std=c++11 -arch=sm_30"
    echo "回退编译参数: $BASIC_FLAGS"
    
    nvcc $BASIC_FLAGS sddmm_compatible.cu -o sddmm_compatible
    
    if [ $? -ne 0 ]; then
        echo "编译仍然失败！请检查 CUDA 环境和代码兼容性。"
        exit 1
    fi
fi

echo "编译成功！"
echo

# 创建测试矩阵文件（如果不存在）
if [ ! -f "test_matrix.mtx" ]; then
    echo "创建测试矩阵文件..."
    
    # 检查是否有 Python
    if command -v python3 &> /dev/null; then
        python3 create_test_matrix.py small mixed
        if [ -f "small_test.mtx" ]; then
            mv small_test.mtx test_matrix.mtx
        fi
    fi
    
    # 如果 Python 脚本失败，创建一个简单的测试矩阵
    if [ ! -f "test_matrix.mtx" ]; then
        echo "创建简单测试矩阵..."
        cat > test_matrix.mtx << 'EOF'
%%MatrixMarket matrix coordinate real general
100 100 1000
1 1 1.0
1 2 2.0
2 1 3.0
2 2 4.0
3 3 5.0
4 4 6.0
5 5 7.0
6 6 8.0
7 7 9.0
8 8 10.0
EOF
        
        # 添加更多随机元素
        for i in {11..1000}; do
            row=$((RANDOM % 100 + 1))
            col=$((RANDOM % 100 + 1))
            val=$(echo "scale=6; $RANDOM / 32767 * 20 - 10" | bc -l 2>/dev/null || echo "1.0")
            echo "$row $col $val" >> test_matrix.mtx
        done
    fi
    
    echo "测试矩阵文件创建完成"
fi

echo "运行兼容性优化版本..."
echo "=========================="

# 运行不同的 K 值进行测试
for K in 32 64 128; do
    echo
    echo "测试 K=$K:"
    echo "----------"
    ./sddmm_compatible test_matrix.mtx $K
    echo
done

echo "=========================="
echo "所有测试完成！"

# 性能对比（如果原版本存在）
if [ -f "sddmm_46" ]; then
    echo
    echo "与原版本性能对比:"
    echo "=================="
    echo "原版本 (K=128):"
    ./sddmm_46 test_matrix.mtx 128 2>/dev/null || echo "原版本运行失败"
    echo
    echo "兼容性优化版本 (K=128):"
    ./sddmm_compatible test_matrix.mtx 128
fi

echo
echo "脚本执行完成！"
